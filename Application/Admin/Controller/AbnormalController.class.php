<?php

namespace Admin\Controller;

use Common\Controller\AdminController;
use Think\Exception;

class AbnormalController extends AdminController
{
    /**
     * [_initialize description]
     */
    public function _initialize()
    {
        parent::_initialize();
        $this->assign('menu_active', 'abnormal_active');
    }

    /**
     * 无流量异常账号列表
     *
     * @return void
     */
    public function noflow()
    {
        $page = I('get.p', 0);
        $filter['keyword'] = I('get.keyword', '');

        $where['type'] = 2;
        if (!empty($filter['keyword'])) {
            $where['kd_account'] = $filter['keyword'];
        }

        $total = M('user_account_exception')
            ->where($where)
            ->count();
        $pagesize = C('PAGE_SIZE');
        $pageObject = new \Org\Util\Page($total, $pagesize);
        $pages = $pageObject->show();

        $exceptionRows = M('user_account_exception')
            ->where($where)
            ->page($page)
            ->limit($pagesize)
            ->select();

        $this->assign('exceptionRows', $exceptionRows);
        $this->assign('pages', $pages);
        $this->assign('filter', $filter);
        $this->assign('pagesize', $pagesize);
        $this->display();
    }

    /**
     * 无流量账号操作停机
     *
     * @return void
     */
    public function noflowShutdown()
    {
        $is_batch = I('get.is_batch',0);
        if ($is_batch == 1) {
            $ids = I('post.ids',[]);
            if (empty($ids)) {
                $this->error("请选择需要操作的账号");
            }

            // 批量操作处理
            $this->_batchNoflowShutdown($ids);
        }else{
            $id = I('get.id','');
            if (empty($id)) {
                $this->error("参数错误");
            }

            // 单个操作处理
            $this->_singleNoflowShutdown($id);
        }

        $this->success("操作成功");
    }

    /**
     * 单个账号停机操作
     *
     * @param int $id 账号ID
     * @return void
     */
    private function _singleNoflowShutdown($id)
    {
        $dataRow = M('user_account_exception')->where(['id'=>$id])->find();
        if (empty($dataRow)) {
            $this->error("没有找到账号信息");
        }
        if ($dataRow['is_stop'] == 1 && $dataRow['is_exec'] == 1) {
            $this->error("请勿重复操作停机");
        }

        $save = [
            'is_stop' => 1,
            'updated_at' => time()
        ];
        $map = [
            'id' => $id,
            'is_stop' => 0,
            'is_exec' => 0
        ];

        $result = M('user_account_exception')->where($map)->save($save);
        if (!$result) {
            $this->error("停机操作失败");
        }
    }

    /**
     * 批量账号停机操作
     *
     * @param array $ids 账号ID数组
     * @return void
     */
    private function _batchNoflowShutdown($ids)
    {
        if (!is_array($ids)) {
            $this->error("参数格式错误");
        }

        // 验证所有ID的有效性
        $validIds = [];
        $invalidIds = [];
        $alreadyStoppedIds = [];

        foreach ($ids as $id) {
            if (empty($id) || !is_numeric($id)) {
                $invalidIds[] = $id;
                continue;
            }

            $dataRow = M('user_account_exception')->where(['id'=>$id])->find();
            if (empty($dataRow)) {
                $invalidIds[] = $id;
                continue;
            }

            if ($dataRow['is_stop'] == 1 && $dataRow['is_exec'] == 1) {
                $alreadyStoppedIds[] = $id;
                continue;
            }

            $validIds[] = $id;
        }

        // 如果有无效ID，返回错误信息
        if (!empty($invalidIds)) {
            $this->error("以下账号ID无效或不存在：" . implode(',', $invalidIds));
        }

        // 如果所有账号都已停机，返回提示
        if (empty($validIds) && !empty($alreadyStoppedIds)) {
            $this->error("所选账号均已停机，无需重复操作");
        }

        // 如果没有需要操作的账号
        if (empty($validIds)) {
            $this->error("没有需要操作的账号");
        }

        // 批量更新数据库
        $save = [
            'is_stop' => 1,
            'updated_at' => time()
        ];
        $map = [
            'id' => ['in', $validIds],
            'is_stop' => 0,
            'is_exec' => 0
        ];

        $result = M('user_account_exception')->where($map)->save($save);
        if (!$result) {
            $this->error("批量停机操作失败");
        }

        // 记录操作日志（可选）
        $this->_logBatchOperation('noflow_shutdown', $validIds, $alreadyStoppedIds);
    }

    /**
     * 记录批量操作日志
     *
     * @param string $operation 操作类型
     * @param array $successIds 成功操作的ID
     * @param array $skippedIds 跳过的ID
     * @return void
     */
    private function _logBatchOperation($operation, $successIds = [], $skippedIds = [])
    {
        $logData = [
            'operation' => $operation,
            'success_count' => count($successIds),
            'success_ids' => implode(',', $successIds),
            'skipped_count' => count($skippedIds),
            'skipped_ids' => implode(',', $skippedIds),
            'operator_id' => session('admin_id'),
            'created_at' => time()
        ];

        // 这里可以根据实际需求记录到日志表中
        // M('operation_log')->add($logData);
    }
}
