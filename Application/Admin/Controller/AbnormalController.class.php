<?php

namespace Admin\Controller;

use Common\Controller\AdminController;
use Think\Exception;

class AbnormalController extends AdminController
{
    /**
     * [_initialize description]
     */
    public function _initialize()
    {
        parent::_initialize();
        $this->assign('menu_active', 'abnormal_active');
    }

    /**
     * 无流量异常账号列表
     *
     * @return void
     */
    public function noflow()
    {
        $page = I('get.p', 0);
        $filter['keyword'] = I('get.keyword', '');

        $where['type'] = 2;
        if (!empty($filter['keyword'])) {
            $where['kd_account'] = $filter['keyword'];
        }

        $total = M('user_account_exception')
            ->where($where)
            ->count();
        $pagesize = C('PAGE_SIZE');
        $pageObject = new \Org\Util\Page($total, $pagesize);
        $pages = $pageObject->show();

        $exceptionRows = M('user_account_exception')
            ->where($where)
            ->page($page)
            ->limit($pagesize)
            ->select();

        $this->assign('exceptionRows', $exceptionRows);
        $this->assign('pages', $pages);
        $this->assign('filter', $filter);
        $this->assign('pagesize', $pagesize);
        $this->display();
    }

    /**
     * 无流量账号操作停机
     *
     * @return void
     */
    public function noflowShutdown()
    {
        $id = I('get.id','');
        if (empty($id)) {
            $this->error("参数错误");
        }
        $dataRow = M('user_account_exception t1')->where(['id'=>$id])->find();
        if (empty($dataRow)) {
            $this->error("没有找到账号信息");
        }
        if ($dataRow['is_stop'] == 1 && $dataRow['is_exec'] == 1) {
            $this->error("请勿重复操作停机");
        }

        $save = [
            'is_stop' => 1,
            'updated_at' => time()
        ];
        $map = [
            'id' => $id,
            'is_stop' => 0,
            'is_exec' => 0
        ];
        M('user_account_exception')->where($map)->save($save);

        $this->success("操作成功");
    }
}
