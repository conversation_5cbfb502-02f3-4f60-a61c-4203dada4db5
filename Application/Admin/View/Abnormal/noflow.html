<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{:C('WEB_TITLE')}}</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="__PUBLIC__/css/bootstrap.min.css?v=3.4.0" rel="stylesheet">
    <link href="__PUBLIC__/css/font-awesome.min.css?v=4.3.0" rel="stylesheet">
    <link href="__PUBLIC__/css/animate.min.css" rel="stylesheet">
    <link href="__PUBLIC__/css/style.min.css?v=3.2.0" rel="stylesheet">
</head>

<body class="gray-bg">
<include file="Common/header"/>
    <div class="wrapper">
        <div class="ibox">
            <div class="ibox-title">
                <h5>无流量异常账号</h5>
                <div class="ibox-tools">
                    <a id="page-refresh" href="javascript:;">
                        <i class="fa fa-refresh"></i> 刷新
                    </a>
                </div>
            </div>
            <div class="ibox-content">
                <div class="row">
                    <div class="col-sm-12 m-b">
                        <form method="get" action="" autocomplete="off">
                            <div class="row">
                                <div class="col-sm-3 m-b">
                                    <div class="input-group">
                                        <div class="input-group-addon gray-bg">是否有停机</div>
                                        <select name="is_stop" class="form-control">
                                            <option value="1" <if condition="$filter.is_stop eq 1">selected="selected"</if>>是</option>
                                            <option value="0" <if condition="$filter.is_stop eq 0">selected="selected"</if>>否</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-sm-3 m-b">
                                    <div class="input-group">
                                        <div class="input-group-addon gray-bg">是否有复机</div>
                                        <select name="is_resume" class="form-control">
                                            <option value="1" <if condition="$filter.is_resume eq 1">selected="selected"</if>>是</option>
                                            <option value="0" <if condition="$filter.is_resume eq 0">selected="selected"</if>>否</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-sm-3 m-b">
                                    <div class="input-group">
                                        <div class="input-group-addon gray-bg">宽带账号</div>
                                        <input type="text" class="form-control" placeholder="请输入宽带账号查询" name="keyword" value="{{$filter.keyword}}" >
                                    </div>
                                </div>
                                <div class="col-sm-3">
                                    <button type="submit" class="btn btn-primary">查询</button>
                                    <a class="btn btn-warning" href="{{:U('noflow')}}">重置</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <form action="{{:U('noflowShutdown',array('is_batch'=>1))}}" method="post" autocomplete="off">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th class="col-sm-1">
                                <div class="checkbox checkbox-inline">
                                    <input id="SAL" type="checkbox" class="check-all">
                                    <label for="SAL">全选</label>
                                </div>
                            </th>
                            <th class="text-center">宽带账号</th>
                            <th class="text-center">是否有停机</th>
                            <th class="text-center">是否有复机</th>
                            <th class="text-center">创建时间</th>
                            <th class="text-center">操作</th>
                        </tr>
                    </thead>
                    <tfoot style="display:none;">
                        <tr>
                            <td colspan="10">
                                <div class="row">
                                    <div class="col-sm-3">
                                        <div class="input-group has-success">
                                            <select name="status" class="form-control">
                                                <option value="1">停机</option>
                                            </select>
                                            <span class="input-group-btn">
                                                <button type="button" class="btn btn-primary">批量处理</button>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </tfoot>
                    <tbody class="tooltip-fn">
                    <volist name="exceptionRows" id="exceptionRow">
                        <tr>
                            <td>
                                <div class="checkbox checkbox-inline">
                                    <input id="SAL{{$exceptionRow.id}}" type="checkbox" class="check-child" name="ids[]" value="{{$exceptionRow.id}}" />
                                    <label for="SAL{{$exceptionRow.id}}">{{$exceptionRow.id}}</label>
                                </div>
                            </td>
                            <td class="text-center">{{$exceptionRow.kd_account}}</td>
                            <td class="text-center">
                                <eq name="exceptionRow.is_stop" value="1">
                                    <span class="text-warning">是</span>
                                <else />
                                    <span class="text-danger">否</span>
                                </eq>
                            </td>
                            <td class="text-center">
                                <eq name="exceptionRow.is_resume" value="1">
                                    <span class="text-warning">是</span>
                                <else />
                                    <span class="text-danger">否</span>
                                </eq>
                            </td>
                            <td class="text-center">{{$exceptionRow.created_at|date="Y-m-d",###}}</td>
                            <td class="text-center">
                                <eq name="exceptionRow.is_stop" value="0">
                                    <a href="{{:U('noflowShutdown',array('id'=>$exceptionRow['id'],'is_batch' => 0))}}" class="btn btn-sm btn-warning">停机</a>
                                <else />
                                    -
                                </eq>
                            </td>
                        </tr>
                    </volist>
                    </tbody>
                </table>
                </form>

                <div class="row">
                    <div class="col-sm-12 pager"><include file="Common/page" /></div>
                </div>

            </div>
        </div>

    </div>

    <!-- 全局js -->
    <script src="__PUBLIC__/js/jquery.min.js?v=2.1.1"></script>
    <script src="__PUBLIC__/js/bootstrap.min.js?v=3.4.0"></script>

    <!-- 自定义js -->
    <script src="__PUBLIC__/js/content.min.js?v={{$time}}"></script>
    <script>
    $(function(){
        var checkbox = $('.check-all, .check-child');
        checkbox.on('click', function(){
            var obj = batch();
            if(obj.num < 1){
                $('tfoot').hide();
            }else{
                $('tfoot').show();
            }
        });

        var submit = $('tfoot').find('button');
        submit.on('click', function(){
            var obj = batch();
            if(obj.num < 1){
                _alert('至少选择一个宽带账号','error');
                return false;
            }else{
                swal({
                    title: "批量处理",
                    text: "当前选中：" + obj.num + " 个宽带账号",
                    //type: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: "#1ab394",
                    confirmButtonText: "确认",
                    cancelButtonText: "取消",
                    closeOnConfirm: false
                },
                function(){
                    submit.parents('form').submit();
                });
            }
        });

        function batch(){
            var obj = {}; obj.num = 0;
            $('.check-child:checked').each(function(){
                obj.num++;
            });
            return obj;
        }
    });
    </script>
</body>
</html>
